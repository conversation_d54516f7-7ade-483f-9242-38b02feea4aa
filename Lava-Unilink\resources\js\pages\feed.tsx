import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import FeedFilters from '@/components/feed-filters';
import CreatePostModal from '@/components/create-post-modal';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Search,
    Filter,
    TrendingUp,
    RefreshCw,
    Users,
    FileText,
    Calendar,
    Settings
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Feed',
        href: '/feed',
    },
];

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: {
        id: number;
        name: string;
        avatar?: string;
    };
    organization?: {
        id: number;
        name: string;
        logo?: string;
    };
    comments_count: number;
    reactions_count: number;
    user_reaction?: string;
    created_at: string;
    published_at: string;
}

interface FeedProps {
    initialPosts?: {
        data: Post[];
        current_page: number;
        last_page: number;
        total: number;
    };
    feedType?: 'personalized' | 'trending' | 'all';
}

function Feed({ initialPosts, feedType = 'personalized' }: FeedProps) {
    const [posts, setPosts] = useState<Post[]>(initialPosts?.data || []);
    const [loading, setLoading] = useState(false);
    const [refreshing, setRefreshing] = useState(false);
    const [hasMore, setHasMore] = useState(initialPosts ? initialPosts.current_page < initialPosts.last_page : true);
    const [currentPage, setCurrentPage] = useState(initialPosts?.current_page || 1);
    const [searchQuery, setSearchQuery] = useState('');
    const [showFilters, setShowFilters] = useState(false);
    const [showCreatePost, setShowCreatePost] = useState(false);
    const [activeTab, setActiveTab] = useState<'personalized' | 'trending' | 'all'>(feedType);

    // Debug: Log the props to see if data is being received
    console.log('Feed component props:', { initialPosts, feedType, posts });
    
    const [filters, setFilters] = useState({
        type: '',
        organization_id: '',
        campus: '',
        date_from: '',
        date_to: '',
        sort_by: 'created_at',
        sort_order: 'desc'
    });

    // Load posts based on active tab and filters
    const loadPosts = useCallback(async (page = 1, append = false) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '15',
                ...(searchQuery && { search: searchQuery }),
                ...Object.fromEntries(Object.entries(filters).filter(([_, v]) => v !== ''))
            });

            let endpoint = '/api/v1/posts';
            if (activeTab === 'personalized') {
                endpoint = '/api/v1/feed';
            } else if (activeTab === 'trending') {
                endpoint = '/api/v1/feed/trending';
            }

            const response = await fetch(`${endpoint}?${params}`, {
                headers: {
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'Accept': 'application/json',
                }
            });

            if (response.ok) {
                const data = await response.json();
                if (append) {
                    setPosts(prev => [...prev, ...data.data]);
                } else {
                    setPosts(data.data);
                }
                setCurrentPage(data.current_page);
                setHasMore(data.current_page < data.last_page);
            }
        } catch (error) {
            console.error('Error loading posts:', error);
        } finally {
            setLoading(false);
        }
    }, [activeTab, searchQuery, filters]);

    // Load more posts for infinite scroll
    const loadMore = useCallback(() => {
        if (!loading && hasMore) {
            loadPosts(currentPage + 1, true);
        }
    }, [loading, hasMore, currentPage, loadPosts]);

    // Refresh feed
    const refreshFeed = useCallback(async () => {
        setRefreshing(true);
        await loadPosts(1, false);
        setRefreshing(false);
    }, [loadPosts]);

    // Handle search
    const handleSearch = useCallback((e: React.FormEvent) => {
        e.preventDefault();
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle filter changes
    const handleFilterChange = useCallback((newFilters: typeof filters) => {
        setFilters(newFilters);
        loadPosts(1, false);
    }, [loadPosts]);

    // Handle tab change
    const handleTabChange = useCallback((tab: 'personalized' | 'trending' | 'all') => {
        setActiveTab(tab);
        setCurrentPage(1);
        setPosts([]);
    }, []);

    // Load posts when tab changes
    useEffect(() => {
        loadPosts(1, false);
    }, [activeTab]);

    // Infinite scroll effect
    useEffect(() => {
        const handleScroll = () => {
            if (window.innerHeight + document.documentElement.scrollTop !== document.documentElement.offsetHeight || loading) {
                return;
            }
            loadMore();
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, [loadMore, loading]);

    // Handle post reactions
    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${document.querySelector('meta[name="api-token"]')?.getAttribute('content')}`,
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { 
                                ...post, 
                                reactions_count: post.user_reaction === reactionType 
                                    ? post.reactions_count - 1 
                                    : post.reactions_count + (post.user_reaction ? 0 : 1),
                                user_reaction: post.user_reaction === reactionType ? undefined : reactionType
                            }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        // Implement share functionality
        navigator.share?.({
            title: 'UniLink Post',
            url: `${window.location.origin}/posts/${postId}`
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Feed" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mx-auto mt-4">
                        <div className="row">
                            {/* Left Sidebar */}
                            <div className="col-md-3 mb-4">
                                <div className="sidebar-content">
                                    <Card className="shadow-sm">
                                        <CardHeader className="bg-unilink-primary text-white">
                                            <h5 className="mb-0 font-semibold">Menu</h5>
                                        </CardHeader>
                                        <CardContent className="p-0">
                                            <div className="list-group list-group-flush">
                                                <Link href="/profile" className="list-group-item list-group-item-action d-flex align-items-center">
                                                    <Users className="w-4 h-4 me-2" />
                                                    Profile
                                                </Link>
                                                <Link href="/organizations" className="list-group-item list-group-item-action d-flex align-items-center">
                                                    <Users className="w-4 h-4 me-2" />
                                                    My Organizations
                                                </Link>
                                                <Link href="/events" className="list-group-item list-group-item-action d-flex align-items-center">
                                                    <Calendar className="w-4 h-4 me-2" />
                                                    Events
                                                </Link>
                                                <Link href="/settings" className="list-group-item list-group-item-action d-flex align-items-center">
                                                    <Settings className="w-4 h-4 me-2" />
                                                    Settings
                                                </Link>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>

                            {/* Main Feed */}
                            <div className="col-md-6 mb-4">
                                {/* Filter Buttons */}
                                <Card className="shadow-sm mb-4">
                                    <CardContent className="p-4">
                                        <div className="d-flex justify-content-between align-items-center">
                                            <div className="d-flex align-items-center">
                                                <Button
                                                    variant={activeTab === 'personalized' ? 'default' : 'outline'}
                                                    size="sm"
                                                    onClick={() => handleTabChange('personalized')}
                                                    className={`me-2 ${activeTab === 'personalized' ? 'btn-primary' : 'btn-outline-primary'}`}
                                                >
                                                    All
                                                </Button>

                                                <Button
                                                    variant="outline"
                                                    size="sm"
                                                    onClick={() => setShowFilters(!showFilters)}
                                                    className="btn-outline-primary"
                                                >
                                                    <Filter className="w-4 h-4 me-1" />
                                                    Filter
                                                </Button>
                                            </div>

                                            <Button
                                                onClick={() => setShowCreatePost(true)}
                                                className="btn-primary"
                                            >
                                                <Plus className="w-4 h-4 me-1" />
                                                Create Post
                                            </Button>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Filters Panel */}
                                {showFilters && (
                                    <FeedFilters
                                        filters={filters}
                                        onFiltersChange={handleFilterChange}
                                        onClose={() => setShowFilters(false)}
                                    />
                                )}

                                {/* Scrollable Feed Content */}
                                <div className="feed-content">
                                    {posts.length > 0 ? (
                                        posts.map((post) => (
                                            <PostCard
                                                key={post.id}
                                                post={post}
                                                onReact={handleReaction}
                                                onComment={handleComment}
                                                onShare={handleShare}
                                            />
                                        ))
                                    ) : !loading ? (
                                        <Card className="shadow-sm mb-4">
                                            <CardContent className="p-4 text-center">
                                                <FileText className="w-12 h-12 text-muted mx-auto mb-4" />
                                                <h3 className="text-lg font-medium text-unilink-darkest mb-2">No posts found</h3>
                                                <p className="text-muted mb-4">
                                                    Be the first to add a post!
                                                </p>
                                                <Button
                                                    onClick={() => setShowCreatePost(true)}
                                                    className="btn-primary"
                                                >
                                                    <Plus className="w-4 h-4 me-2" />
                                                    Create Post
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    ) : null}

                                    {/* Loading Skeletons */}
                                    {loading && (
                                        <div className="space-y-4">
                                            {[...Array(3)].map((_, i) => (
                                                <Card key={i} className="shadow-sm mb-4">
                                                    <div className="p-4">
                                                        <div className="d-flex align-items-center mb-3">
                                                            <Skeleton className="w-10 h-10 rounded-circle me-3" />
                                                            <div>
                                                                <Skeleton className="h-4 w-32 mb-2" />
                                                                <Skeleton className="h-3 w-24" />
                                                            </div>
                                                        </div>
                                                        <Skeleton className="h-6 w-75 mb-3" />
                                                        <Skeleton className="h-20 w-100 mb-3" />
                                                        <div className="d-flex">
                                                            <Skeleton className="h-8 w-16 me-2" />
                                                            <Skeleton className="h-8 w-16 me-2" />
                                                            <Skeleton className="h-8 w-16" />
                                                        </div>
                                                    </div>
                                                </Card>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* Right Sidebar */}
                            <div className="col-md-3 mb-4">
                                <div className="space-y-4">
                                    <Card className="shadow-sm">
                                        <CardHeader className="bg-unilink-primary text-white">
                                            <h5 className="mb-0 font-semibold">Upcoming Events</h5>
                                        </CardHeader>
                                        <CardContent className="p-4">
                                            <div className="space-y-3">
                                                <div className="text-sm text-muted">
                                                    Enrollment Period: Aug 1-15
                                                </div>
                                                <div className="text-sm text-muted">
                                                    Orientation Day: Aug 20
                                                </div>
                                                <div className="text-sm text-muted">
                                                    First Day of Classes: Aug 22
                                                </div>
                                                <div className="text-sm text-muted">
                                                    Foundation Day: Sept 15
                                                </div>
                                            </div>
                                        </CardContent>
                                    </Card>

                                    <Card className="shadow-sm">
                                        <CardHeader className="bg-unilink-primary text-white">
                                            <h5 className="mb-0 font-semibold">Quick Links</h5>
                                        </CardHeader>
                                        <CardContent className="p-0">
                                            <div className="list-group list-group-flush">
                                                <a href="https://sksu.edu.ph/" target="_blank" rel="noopener noreferrer" className="list-group-item list-group-item-action">
                                                    University Website
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Student Portal
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Library Resources
                                                </a>
                                                <a href="#" className="list-group-item list-group-item-action">
                                                    Academic Calendar
                                                </a>
                                            </div>
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Create Post Modal */}
            {showCreatePost && (
                <CreatePostModal
                    onClose={() => setShowCreatePost(false)}
                    onPostCreated={(newPost) => {
                        setPosts(prev => [newPost, ...prev]);
                        setShowCreatePost(false);
                    }}
                />
            )}
        </AppLayout>
    );
}

export default Feed;
