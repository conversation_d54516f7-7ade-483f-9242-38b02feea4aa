import { useState, useEffect } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Users,
    FileText,
    MessageCircle,
    BookOpen,
    TrendingUp
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface DashboardProps {
    user?: any;
    userOrganizations?: any[];
    recentPosts?: any[];
    enrolledCourses?: any[];
    stats?: any;
    trendingOrganizations?: any[];
}

export default function Dashboard({
    user,
    userOrganizations = [],
    recentPosts = [],
    enrolledCourses = [],
    stats = {},
    trendingOrganizations = []
}: DashboardProps) {
    const [posts, setPosts] = useState(recentPosts);
    const [loading, setLoading] = useState(false);

    // Load dashboard data if not provided as props
    useEffect(() => {
        if (!user) {
            loadDashboardData();
        }
    }, []);

    const loadDashboardData = async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/v1/dashboard/data');
            if (response.ok) {
                const data = await response.json();
                setPosts(data.posts?.data || []);
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { ...post, reactions_count: post.reactions_count + (post.user_reacted ? -1 : 1) }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        const url = `${window.location.origin}/posts/${postId}`;
        navigator.clipboard.writeText(url).then(() => {
            alert('Post URL copied to clipboard!');
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="main-container">
                <div className="content-area">
                    <div className="container mt-4">
                        {/* Welcome Header */}
                        <div className="mb-4">
                            <h1 className="h2">
                                Welcome to UniLink!
                            </h1>
                            <p className="text-muted">
                                Connect with your university community
                            </p>
                        </div>

                        {/* Quick Stats */}
                        <div className="row g-4 mb-4">
                            <div className="col-md-3">
                                <div className="card shadow-sm">
                                    <div className="card-body">
                                        <div className="d-flex align-items-center">
                                            <FileText className="w-8 h-8 text-primary me-3" />
                                            <div>
                                                <p className="small text-muted mb-0">Posts</p>
                                                <p className="h4 mb-0">{stats?.total_posts || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="col-md-3">
                                <div className="card shadow-sm">
                                    <div className="card-body">
                                        <div className="d-flex align-items-center">
                                            <MessageCircle className="w-8 h-8 text-success me-3" />
                                            <div>
                                                <p className="small text-muted mb-0">Comments</p>
                                                <p className="h4 mb-0">{stats?.total_comments || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="col-md-3">
                                <div className="card shadow-sm">
                                    <div className="card-body">
                                        <div className="d-flex align-items-center">
                                            <Users className="w-8 h-8 text-info me-3" />
                                            <div>
                                                <p className="small text-muted mb-0">Organizations</p>
                                                <p className="h4 mb-0">{stats?.organizations_count || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="col-md-3">
                                <div className="card shadow-sm">
                                    <div className="card-body">
                                        <div className="d-flex align-items-center">
                                            <BookOpen className="w-8 h-8 text-warning me-3" />
                                            <div>
                                                <p className="small text-muted mb-0">Courses</p>
                                                <p className="h4 mb-0">{stats?.courses_count || 0}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {/* Quick Actions */}
                        <div className="card shadow-sm mb-4">
                            <div className="card-header">
                                <h5 className="mb-0">Quick Actions</h5>
                            </div>
                            <div className="card-body">
                                <div className="d-flex flex-wrap gap-2">
                                    <Link href="/feed" className="btn btn-primary">
                                        <TrendingUp className="w-4 h-4 me-2" />
                                        View Feed
                                    </Link>
                                    <Link href="/posts/create" className="btn btn-outline-primary">
                                        <Plus className="w-4 h-4 me-2" />
                                        Create Post
                                    </Link>
                                    <Link href="/organizations/create" className="btn btn-outline-primary">
                                        <Users className="w-4 h-4 me-2" />
                                        Create Organization
                                    </Link>
                                    <Link href="/organizations" className="btn btn-outline-primary">
                                        <Users className="w-4 h-4 me-2" />
                                        Browse Organizations
                                    </Link>
                                    <Link href="/courses" className="btn btn-outline-primary">
                                        <BookOpen className="w-4 h-4 me-2" />
                                        Browse Courses
                                    </Link>
                                </div>
                            </div>
                        </div>

                        {/* Recent Activity */}
                        <div className="row">
                            {/* Posts Feed */}
                            <div className="col-lg-8">
                                <div className="d-flex justify-content-between align-items-center mb-3">
                                    <h2 className="h5 mb-0">Recent Posts</h2>
                                    <Link href="/posts" className="btn btn-sm btn-outline-primary">
                                        View all posts
                                    </Link>
                                </div>

                                <div className="posts-feed">
                                    {posts.length > 0 ? (
                                        posts.slice(0, 5).map((post) => (
                                            <PostCard
                                                key={post.id}
                                                post={post}
                                                onReact={handleReaction}
                                                onComment={handleComment}
                                                onShare={handleShare}
                                            />
                                        ))
                                    ) : (
                                        <div className="card shadow-sm">
                                            <div className="card-body text-center py-5">
                                                <FileText className="w-12 h-12 text-muted mx-auto mb-3" />
                                                <h3 className="h6 mb-2">No posts yet</h3>
                                                <p className="text-muted mb-3">
                                                    Join some organizations to see posts in your feed.
                                                </p>
                                                <Link href="/organizations" className="btn btn-primary">
                                                    Browse Organizations
                                                </Link>
                                            </div>
                                        </div>
                                    )}
                                </div>
                    </div>

                            </div>

                            {/* Sidebar */}
                            <div className="col-lg-4">
                                {/* Organizations */}
                                <div className="card shadow-sm mb-4">
                                    <div className="card-header">
                                        <h5 className="mb-0">My Organizations</h5>
                                    </div>
                                    <div className="card-body">
                                        {userOrganizations.length > 0 ? (
                                            <div className="list-group list-group-flush">
                                                {userOrganizations.slice(0, 3).map((org) => (
                                                    <Link
                                                        key={org.id}
                                                        href={`/organizations/${org.id}`}
                                                        className="list-group-item list-group-item-action d-flex align-items-center"
                                                    >
                                                        <div className="w-8 h-8 bg-light rounded-circle d-flex align-items-center justify-content-center me-3">
                                                            <Users className="w-4 h-4 text-muted" />
                                                        </div>
                                                        <div className="flex-grow-1">
                                                            <p className="mb-0 small fw-medium">
                                                                {org.name}
                                                            </p>
                                                            <p className="mb-0 text-muted" style={{fontSize: '0.75rem'}}>
                                                                {org.members_count} members
                                                            </p>
                                                        </div>
                                                    </Link>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-4">
                                                <Users className="w-8 h-8 text-muted mx-auto mb-2" />
                                                <p className="small text-muted mb-3">
                                                    Join organizations to connect with peers
                                                </p>
                                                <Link href="/organizations" className="btn btn-sm btn-primary">
                                                    Browse Organizations
                                                </Link>
                                            </div>
                                        )}
                                    </div>
                                </div>

                                {/* Courses */}
                                <div className="card shadow-sm">
                                    <div className="card-header">
                                        <h5 className="mb-0">My Courses</h5>
                                    </div>
                                    <div className="card-body">
                                        {enrolledCourses.length > 0 ? (
                                            <div className="list-group list-group-flush">
                                                {enrolledCourses.slice(0, 3).map((course) => (
                                                    <Link
                                                        key={course.id}
                                                        href={`/courses/${course.id}`}
                                                        className="list-group-item list-group-item-action"
                                                    >
                                                        <div className="d-flex justify-content-between align-items-center">
                                                            <div>
                                                                <p className="mb-0 small fw-medium">
                                                                    {course.code}
                                                                </p>
                                                                <p className="mb-0 text-muted text-truncate" style={{fontSize: '0.75rem'}}>
                                                                    {course.name}
                                                                </p>
                                                            </div>
                                                            <span className="badge bg-secondary">
                                                                {course.credits} cr
                                                            </span>
                                                        </div>
                                                    </Link>
                                                ))}
                                            </div>
                                        ) : (
                                            <div className="text-center py-4">
                                                <BookOpen className="w-8 h-8 text-muted mx-auto mb-2" />
                                                <p className="small text-muted mb-3">
                                                    Enroll in courses to track your progress
                                                </p>
                                                <Link href="/courses" className="btn btn-sm btn-primary">
                                                    Browse Courses
                                                </Link>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
