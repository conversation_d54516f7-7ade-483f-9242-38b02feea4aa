import { useState } from 'react';
import { <PERSON> } from '@inertiajs/react';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
    Heart, 
    MessageCircle, 
    Share2, 
    MoreHorizontal, 
    Pin,
    Calendar,
    Users
} from 'lucide-react';

interface User {
    id: number;
    name: string;
    avatar?: string;
}

interface Organization {
    id: number;
    name: string;
    logo?: string;
}

interface Post {
    id: number;
    title: string;
    content: string;
    type: string;
    visibility: string;
    is_pinned: boolean;
    comments_enabled: boolean;
    media?: Array<{
        path: string;
        name: string;
        type: string;
    }>;
    user: User;
    organization?: Organization;
    comments_count: number;
    reactions_count: number;
    created_at: string;
    published_at: string;
}

interface PostCardProps {
    post: Post;
    onReact?: (postId: number, reactionType: string) => void;
    onComment?: (postId: number) => void;
    onShare?: (postId: number) => void;
}

export default function PostCard({ post, onReact, onComment, onShare }: PostCardProps) {
    const [isExpanded, setIsExpanded] = useState(false);
    const [showFullContent, setShowFullContent] = useState(false);
    const [userReaction, setUserReaction] = useState<string | null>(null);

    const getTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'announcement': return 'bg-unilink-primary text-white';
            case 'event': return 'bg-blue-500 text-white';
            case 'discussion': return 'bg-green-500 text-white';
            case 'news': return 'bg-orange-500 text-white';
            default: return 'bg-unilink-secondary text-white';
        }
    };

    const shouldShowReadMore = post.content.length > 300;

    const truncateContent = (content: string) => {
        return content.length > 300 ? content.substring(0, 300) + '...' : content;
    };

    const getVisibilityIcon = (visibility: string) => {
        switch (visibility) {
            case 'public': return '🌐';
            case 'members_only': return '👥';
            case 'private': return '🔒';
            default: return '🌐';
        }
    };

    const formatDate = (dateString: string) => {
        const date = new Date(dateString);
        const now = new Date();
        const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

        if (diffInHours < 1) {
            const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
            return `${diffInMinutes}m ago`;
        } else if (diffInHours < 24) {
            return `${diffInHours}h ago`;
        } else if (diffInHours < 168) { // 7 days
            const diffInDays = Math.floor(diffInHours / 24);
            return `${diffInDays}d ago`;
        } else {
            return date.toLocaleDateString();
        }
    };

    const handleReaction = (reactionType: string) => {
        if (onReact) {
            onReact(post.id, reactionType);
            setUserReaction(userReaction === reactionType ? null : reactionType);
        }
    };



    return (
        <Card className="shadow-sm mb-4 post">
            <CardHeader className="bg-light">
                <div className="d-flex justify-content-between align-items-center">
                    <div className="d-flex align-items-center">
                        <img
                            src={post.user.avatar ? `/storage/${post.user.avatar}` : '/img/profilepic.jpg'}
                            className="rounded-circle me-2"
                            width="40"
                            height="40"
                            style={{objectFit: 'cover'}}
                            alt="Profile"
                        />
                        <div>
                            <h5 className="card-title mb-0">{post.title}</h5>
                            <small className="text-muted">
                                Posted by {post.user.name} on {formatDate(post.published_at || post.created_at)}
                                {post.visibility !== 'public' && (
                                    <span className="ms-2">
                                        <i className="fas fa-lock text-muted"></i>
                                        {post.visibility === 'organization' ? ' Organization Only' : ' Campus Only'}
                                    </span>
                                )}
                            </small>
                        </div>
                    </div>
                    {post.organization && (
                        <Badge className="bg-unilink-primary text-white">{post.organization.name}</Badge>
                    )}
                </div>
            </CardHeader>

            <CardContent className="card-body">
                {/* Post Content */}
                <div className="mb-3">
                    <p className="card-text" style={{whiteSpace: 'pre-wrap'}}>
                        {showFullContent ? post.content : truncateContent(post.content)}
                    </p>
                    {shouldShowReadMore && (
                        <button
                            onClick={() => setShowFullContent(!showFullContent)}
                            className="btn btn-link p-0 text-decoration-none"
                            style={{color: 'var(--color-third-darkest)', fontSize: '0.875rem'}}
                        >
                            {showFullContent ? 'Show Less' : 'Read More'}
                        </button>
                    )}
                </div>

                <div className="d-flex align-items-center mb-3">
                    <Badge className={getTypeColor(post.type)}>
                        {post.type}
                    </Badge>
                    {post.is_pinned && (
                        <Badge className="bg-warning text-dark ms-2">
                            <Pin className="w-3 h-3 me-1" />
                            Pinned
                        </Badge>
                    )}
                </div>

                {/* Media */}
                {post.media && post.media.length > 0 && (
                    <div className="mb-3">
                        <div className="row g-2">
                            {post.media.slice(0, 4).map((media, index) => (
                                <div key={index} className="col-6 position-relative">
                                    {media.type.startsWith('image/') ? (
                                        <img
                                            src={`/storage/${media.path}`}
                                            alt={media.name}
                                            className="w-100 rounded"
                                            style={{height: '128px', objectFit: 'cover'}}
                                        />
                                    ) : (
                                        <div className="w-100 bg-light rounded d-flex align-items-center justify-content-center" style={{height: '128px'}}>
                                            <div className="text-center">
                                                <div style={{fontSize: '2rem'}} className="mb-1">📄</div>
                                                <p className="small text-muted text-truncate px-2">
                                                    {media.name}
                                                </p>
                                            </div>
                                        </div>
                                    )}
                                    {index === 3 && post.media.length > 4 && (
                                        <div className="position-absolute top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 rounded d-flex align-items-center justify-content-center">
                                            <span className="text-white fw-semibold">
                                                +{post.media.length - 4} more
                                            </span>
                                        </div>
                                    )}
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {/* Actions */}
                <div className="d-flex justify-content-between align-items-center pt-3 border-top">
                    <div className="d-flex align-items-center">
                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleReaction('like')}
                            className={`btn btn-sm me-3 d-flex align-items-center ${
                                userReaction === 'like' ? 'text-danger' : 'text-muted'
                            }`}
                            style={{border: 'none', background: 'none'}}
                        >
                            <Heart className={`w-4 h-4 me-1 ${userReaction === 'like' ? 'fill-current' : ''}`} />
                            <span>{post.reactions_count}</span>
                        </Button>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onComment?.(post.id)}
                            className="btn btn-sm me-3 d-flex align-items-center text-muted"
                            disabled={!post.comments_enabled}
                            style={{border: 'none', background: 'none'}}
                        >
                            <MessageCircle className="w-4 h-4 me-1" />
                            <span>{post.comments_count}</span>
                        </Button>

                        <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onShare?.(post.id)}
                            className="btn btn-sm d-flex align-items-center text-muted"
                            style={{border: 'none', background: 'none'}}
                        >
                            <Share2 className="w-4 h-4 me-1" />
                            <span>Share</span>
                        </Button>
                    </div>

                    {post.type === 'event' && (
                        <div className="d-flex align-items-center small text-muted">
                            <Calendar className="w-3 h-3 me-1" />
                            Event
                        </div>
                    )}
                </div>
            </CardContent>
        </Card>
    );
}
