@tailwind base;
@tailwind components;
@tailwind utilities;

/* UniLink Color Variables - matching prototype */
:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */

    /* Tailwind CSS variables */
    --background: 238 238 238; /* #EEEEEE */
    --foreground: 34 40 49; /* #222831 */

    --primary: 123 199 77; /* #7BC74D */
    --primary-foreground: 255 255 255;

    --secondary: 57 62 70; /* #393E46 */
    --secondary-foreground: 238 238 238;

    --muted: 220 10% 90%;
    --muted-foreground: 220 10% 40%;

    --accent: 123 199 77; /* #7BC74D */
    --accent-foreground: 255 255 255;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 10% 85%;
    --input: 220 10% 85%;
    --ring: 123 199 77; /* #7BC74D */

    --radius: 0.5rem;

    /* Sidebar variables */
    --sidebar-background: 238 238 238; /* #EEEEEE */
    --sidebar-foreground: 34 40 49; /* #222831 */
    --sidebar-primary: 123 199 77; /* #7BC74D */
    --sidebar-primary-foreground: 255 255 255;
    --sidebar-accent: 123 199 77; /* #7BC74D */
    --sidebar-accent-foreground: 255 255 255;
    --sidebar-border: 220 10% 85%;
    --sidebar-ring: 123 199 77; /* #7BC74D */

    /* Card variables */
    --card: 255 255 255;
    --card-foreground: 34 40 49; /* #222831 */
}

  .dark {
    --color-lightest: #222831; /* Dark Navy for dark mode background */
    --color-third-darkest: #7BC74D; /* Green stays the same */
    --color-second-darkest: #EEEEEE; /* Light Gray for dark mode text */
    --color-darkest: #EEEEEE; /* Light Gray for dark mode text */

    --background: 34 40 49; /* #222831 */
    --foreground: 238 238 238; /* #EEEEEE */

    --primary: 123 199 77; /* #7BC74D */
    --primary-foreground: 34 40 49; /* #222831 */

    --secondary: 57 62 70; /* #393E46 */
    --secondary-foreground: 238 238 238; /* #EEEEEE */

    --muted: 57 62 70; /* #393E46 */
    --muted-foreground: 238 238 238; /* #EEEEEE */

    --accent: 123 199 77; /* #7BC74D */
    --accent-foreground: 34 40 49; /* #222831 */

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 57 62 70; /* #393E46 */
    --input: 57 62 70; /* #393E46 */
    --ring: 123 199 77; /* #7BC74D */

    /* Sidebar variables for dark mode */
    --sidebar-background: 34 40 49; /* #222831 */
    --sidebar-foreground: 238 238 238; /* #EEEEEE */
    --sidebar-primary: 123 199 77; /* #7BC74D */
    --sidebar-primary-foreground: 34 40 49; /* #222831 */
    --sidebar-accent: 123 199 77; /* #7BC74D */
    --sidebar-accent-foreground: 34 40 49; /* #222831 */
    --sidebar-border: 57 62 70; /* #393E46 */
    --sidebar-ring: 123 199 77; /* #7BC74D */

    /* Card variables for dark mode */
    --card: 57 62 70; /* #393E46 */
    --card-foreground: 238 238 238; /* #EEEEEE */
  }
}

/* General styles matching prototype */
body {
    background-color: var(--color-lightest);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--color-darkest);
}

/* Button styling matching prototype */
.btn-primary {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: white !important;
}

.btn-primary:hover {
    background-color: #6AB33C !important;
    border-color: #6AB33C !important;
    color: white !important;
}

.btn-outline-primary {
    color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    background-color: transparent !important;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
    border-color: var(--color-third-darkest) !important;
}

/* Card styling matching prototype */
.card {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-header.bg-primary {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
}

/* Post styling matching prototype */
.post {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

/* Feed content adjustments matching prototype */
.feed-content {
    max-width: 540px;
    margin: 0 auto;
    max-height: calc(100vh - 150px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
}

.feed-content .card {
    width: 100%;
}

/* Main container matching prototype */
.main-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.content-area {
    flex: 1;
    overflow: hidden;
    position: relative;
    padding-bottom: 20px;
}

/* Sidebar scrolling matching prototype */
.sidebar-content {
    max-height: calc(100vh - 100px);
    overflow-y: auto;
    overflow-x: hidden;
}

/* Bootstrap-style classes with UniLink colors */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -0.75rem;
    margin-left: -0.75rem;
}

.col-md-3, .col-md-6 {
    position: relative;
    width: 100%;
    padding-right: 0.75rem;
    padding-left: 0.75rem;
}

@media (min-width: 768px) {
    .col-md-3 {
        flex: 0 0 25%;
        max-width: 25%;
    }
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

.list-group-flush {
    border-radius: 0;
}

.list-group-flush .list-group-item {
    border-right: 0;
    border-left: 0;
    border-radius: 0;
}

.list-group-flush .list-group-item:first-child {
    border-top: 0;
}

.list-group-flush .list-group-item:last-child {
    border-bottom: 0;
}

.list-group-item {
    position: relative;
    display: block;
    padding: 0.75rem 1.25rem;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.125);
    text-decoration: none;
    color: var(--color-darkest);
}

.list-group-item-action {
    width: 100%;
    color: #495057;
    text-align: inherit;
}

.list-group-item-action:hover,
.list-group-item-action:focus {
    z-index: 1;
    color: #495057;
    text-decoration: none;
    background-color: #f8f9fa;
}

/* UniLink specific background colors */
.bg-unilink-primary {
    background-color: var(--color-third-darkest) !important;
    color: white !important;
}

/* Bootstrap utility classes */
.mb-4 {
    margin-bottom: 1.5rem !important;
}

.me-2 {
    margin-right: 0.5rem !important;
}

.me-3 {
    margin-right: 1rem !important;
}

.d-flex {
    display: flex !important;
}

.align-items-center {
    align-items: center !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.text-muted {
    color: #6c757d !important;
}

.w-75 {
    width: 75% !important;
}

.w-100 {
    width: 100% !important;
}

.rounded-circle {
    border-radius: 50% !important;
}

/* Dark mode styles matching prototype */
.dark body {
    background-color: var(--color-lightest);
    color: var(--color-darkest);
}

.dark .card {
    background-color: var(--color-second-darkest);
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .card-header.bg-primary {
    background-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .post {
    background-color: var(--color-second-darkest);
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.1);
}

.dark .btn-primary {
    background-color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .btn-primary:hover {
    background-color: #6AB33C !important;
    border-color: #6AB33C !important;
    color: var(--color-darkest) !important;
}

.dark .btn-outline-primary {
    color: var(--color-third-darkest) !important;
    border-color: var(--color-third-darkest) !important;
}

.dark .btn-outline-primary:hover {
    background-color: var(--color-third-darkest) !important;
    color: var(--color-darkest) !important;
}

.dark .text-muted {
    color: rgba(238, 238, 238, 0.6) !important;
}

.dark input,
.dark textarea,
.dark select {
    background-color: #2c3037;
    color: var(--color-lightest);
    border-color: rgba(238, 238, 238, 0.2);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
    background-color: #2c3037;
    color: var(--color-lightest);
    border-color: var(--color-third-darkest);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
}

